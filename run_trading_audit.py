#!/usr/bin/env python3
"""
Trading System Audit Script
Runs the EnhancedBacktestEngine to observe complete workflow execution
"""

import logging
import sys
from datetime import datetime, timedelta

# Import the trading system
import importlib.util
import sys

# Load the module with space in filename
spec = importlib.util.spec_from_file_location("option_colab", "Option Colab.py")
option_colab = importlib.util.module_from_spec(spec)
sys.modules["option_colab"] = option_colab
spec.loader.exec_module(option_colab)

# Import the class
EnhancedBacktestEngine = option_colab.EnhancedBacktestEngine

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('trading_audit.log')
    ]
)

def run_trading_audit():
    """Run a single day trading simulation for audit purposes"""
    
    print("🔍 TRADING SYSTEM AUDIT - COMPREHENSIVE WORKFLOW EXECUTION")
    print("=" * 80)
    
    # Use recent dates for the audit (last few business days)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3)  # Just a few days for audit
    
    # Format dates
    start_str = start_date.strftime('%Y-%m-%d')
    end_str = end_date.strftime('%Y-%m-%d')
    
    print(f"📅 Audit Period: {start_str} to {end_str}")
    print(f"🎯 Purpose: Observe complete multi-agent workflow execution")
    print("=" * 80)
    
    try:
        # Test both standard and SteeringConf modes
        print("\n🤖 TESTING STANDARD CHIEF TRADER MODE")
        print("-" * 50)
        
        # Standard mode
        engine_standard = EnhancedBacktestEngine(
            start_date=start_str,
            end_date=end_str,
            use_steeringconf=False
        )
        
        # Run just one simulation step for audit
        current_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        engine_standard.run_simulation_step(current_date)
        
        print("\n🎯 TESTING STEERINGCONF CHIEF TRADER MODE")
        print("-" * 50)
        
        # SteeringConf mode
        engine_steeringconf = EnhancedBacktestEngine(
            start_date=start_str,
            end_date=end_str,
            use_steeringconf=True
        )
        
        # Run just one simulation step for audit
        engine_steeringconf.run_simulation_step(current_date)
        
        print("\n✅ AUDIT EXECUTION COMPLETED")
        print("📊 Check trading_audit.log for detailed execution logs")
        print("🔍 Review agent outputs and workflow patterns above")
        
    except Exception as e:
        print(f"\n❌ AUDIT EXECUTION ERROR: {e}")
        logging.error(f"Audit execution failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_trading_audit()
